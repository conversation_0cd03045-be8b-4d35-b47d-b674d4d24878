<!-- SEO Optimization Template for EDEN Health Care -->
<!-- This file contains reusable SEO elements that can be included in all pages -->

<!-- Performance Optimization Meta Tags -->
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="format-detection" content="telephone=yes">
<meta name="mobile-web-app-capable" content="yes">
<meta name="apple-mobile-web-app-capable" content="yes">
<meta name="apple-mobile-web-app-status-bar-style" content="default">
<meta name="msapplication-tap-highlight" content="no">

<!-- Security Headers -->
<meta http-equiv="X-Content-Type-Options" content="nosniff">
<meta http-equiv="X-Frame-Options" content="SAMEORIGIN">
<meta http-equiv="X-XSS-Protection" content="1; mode=block">
<meta http-equiv="Referrer-Policy" content="strict-origin-when-cross-origin">

<!-- DNS Prefetch for External Resources -->
<link rel="dns-prefetch" href="//fonts.googleapis.com">
<link rel="dns-prefetch" href="//cdnjs.cloudflare.com">
<link rel="dns-prefetch" href="//images.unsplash.com">
<link rel="dns-prefetch" href="//www.google.com">
<link rel="dns-prefetch" href="//maps.googleapis.com">

<!-- Preconnect for Critical Resources -->
<link rel="preconnect" href="https://fonts.googleapis.com" crossorigin>
<link rel="preconnect" href="https://cdnjs.cloudflare.com" crossorigin>

<!-- Critical CSS Preload -->
<link rel="preload" href="css/optimized.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
<noscript><link rel="stylesheet" href="css/optimized.css"></noscript>

<!-- JavaScript Preload -->
<link rel="preload" href="js/optimized.js" as="script">

<!-- Favicon and App Icons -->
<link rel="icon" type="image/png" sizes="32x32" href="images/Eden%20logo%20PNG.png">
<link rel="icon" type="image/png" sizes="16x16" href="images/Eden%20logo%20PNG.png">
<link rel="apple-touch-icon" sizes="180x180" href="images/Eden%20logo%20PNG.png">
<link rel="mask-icon" href="images/Eden%20logo%20PNG.png" color="#0056b3">

<!-- Web App Manifest -->
<link rel="manifest" href="manifest.json">

<!-- Theme Colors -->
<meta name="theme-color" content="#0056b3">
<meta name="msapplication-TileColor" content="#0056b3">
<meta name="msapplication-config" content="browserconfig.xml">

<!-- Additional SEO Meta Tags -->
<meta name="language" content="English">
<meta name="revisit-after" content="7 days">
<meta name="distribution" content="global">
<meta name="rating" content="general">
<meta name="copyright" content="EDEN Health Care Pvt. Ltd.">

<!-- Geo Location Tags -->
<meta name="geo.region" content="IN-GJ">
<meta name="geo.placename" content="Ahmedabad">
<meta name="geo.position" content="23.026032;72.520380">
<meta name="ICBM" content="23.026032, 72.520380">

<!-- Business Information -->
<meta name="contact" content="<EMAIL>">
<meta name="reply-to" content="<EMAIL>">
<meta name="owner" content="EDEN Health Care Pvt. Ltd.">

<!-- Social Media Optimization -->
<meta property="fb:app_id" content="your-facebook-app-id">
<meta name="twitter:site" content="@edenhealthcare">
<meta name="twitter:creator" content="@edenhealthcare">

<!-- Additional Open Graph Tags -->
<meta property="og:locale" content="en_US">
<meta property="og:locale:alternate" content="hi_IN">
<meta property="og:locale:alternate" content="gu_IN">

<!-- Article/Page Specific (to be customized per page) -->
<meta property="article:author" content="EDEN Health Care Pvt. Ltd.">
<meta property="article:publisher" content="https://www.facebook.com/edenhealthcare">
<meta property="article:section" content="Healthcare">
<meta property="article:tag" content="Pharmaceuticals">

<!-- Schema.org Breadcrumb (to be customized per page) -->
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "BreadcrumbList",
  "itemListElement": [
    {
      "@type": "ListItem",
      "position": 1,
      "name": "Home",
      "item": "https://edenhealthcare.com/"
    }
  ]
}
</script>

<!-- Performance Monitoring -->
<script>
// Performance monitoring
if ('performance' in window) {
    window.addEventListener('load', function() {
        setTimeout(function() {
            const perfData = performance.getEntriesByType('navigation')[0];
            if (perfData) {
                console.log('Page Load Time:', perfData.loadEventEnd - perfData.loadEventStart, 'ms');
                console.log('DOM Content Loaded:', perfData.domContentLoadedEventEnd - perfData.domContentLoadedEventStart, 'ms');
            }
        }, 0);
    });
}

// Critical resource hints
if ('serviceWorker' in navigator) {
    window.addEventListener('load', function() {
        navigator.serviceWorker.register('/sw.js').then(function(registration) {
            console.log('ServiceWorker registration successful');
        }).catch(function(err) {
            console.log('ServiceWorker registration failed');
        });
    });
}
</script>

<!-- Structured Data for Organization (Global) -->
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "Organization",
  "name": "EDEN Health Care Pvt. Ltd.",
  "alternateName": "EDEN Pharmaceuticals",
  "url": "https://edenhealthcare.com/",
  "logo": "https://edenhealthcare.com/images/Eden%20logo%20PNG.png",
  "contactPoint": {
    "@type": "ContactPoint",
    "telephone": "+91-**********",
    "contactType": "customer service",
    "areaServed": "IN",
    "availableLanguage": ["English", "Hindi", "Gujarati"]
  },
  "address": {
    "@type": "PostalAddress",
    "streetAddress": "301, Abhishree Complex, Opp Star Bazar, Satellite Road",
    "addressLocality": "Ahmedabad",
    "addressRegion": "Gujarat",
    "postalCode": "380015",
    "addressCountry": "IN"
  },
  "sameAs": [
    "https://www.facebook.com/edenhealthcare",
    "https://www.linkedin.com/company/eden-health-care",
    "https://twitter.com/edenhealthcare"
  ]
}
</script>
